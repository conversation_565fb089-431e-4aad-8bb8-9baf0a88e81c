import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/features/video_posters/models/video_poster_carousel.dart';
import 'package:praja/features/video_posters/video_poster_carousel_view_model.dart';
import 'package:praja/features/video_posters/video_poster_carousel_widget.dart';
import 'package:praja/features/video_posters/widgets/video_poster_element_widget.dart';
import 'package:praja/test/mocks/video_poster_carousel_mocks.dart';

void main() {
  group('Video Poster User Photo Tests', () {
    testWidgets('should show camera icon on user photo element', (WidgetTester tester) async {
      // Get mock carousel with user photo
      final mockCarousel = getMockVideoPosterCarousel();
      
      // Verify mock has user photo element
      final userPhotoElement = mockCarousel.elements.firstWhere((element) => element.isUserPhoto);
      expect(userPhotoElement.isUserPhoto, isTrue);

      // Build widget
      await tester.pumpWidget(
        MaterialApp(
          home: ViewModelScope(
            builder: (_) => Scaffold(
              body: VideoPosterCarouselWidget(
                carousel: mockCarousel,
                source: 'test',
                pageIndex: 0,
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify camera icon is visible initially
      expect(find.byIcon(Icons.camera_alt), findsOneWidget);
    });

    testWidgets('should hide camera icon after configured time', (WidgetTester tester) async {
      // Get mock carousel with user photo
      final mockCarousel = getMockVideoPosterCarousel();

      // Build widget
      await tester.pumpWidget(
        MaterialApp(
          home: ViewModelScope(
            builder: (_) => Scaffold(
              body: VideoPosterCarouselWidget(
                carousel: mockCarousel,
                source: 'test',
                pageIndex: 0,
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify camera icon is visible initially
      expect(find.byIcon(Icons.camera_alt), findsOneWidget);

      // Wait for camera icon to disappear (3 seconds + some buffer)
      await tester.pump(const Duration(seconds: 4));

      // Verify camera icon is hidden
      expect(find.byIcon(Icons.camera_alt), findsNothing);
    });

    testWidgets('should show camera icon again when user photo is tapped', (WidgetTester tester) async {
      // Get mock carousel with user photo
      final mockCarousel = getMockVideoPosterCarousel();

      // Build widget
      await tester.pumpWidget(
        MaterialApp(
          home: ViewModelScope(
            builder: (_) => Scaffold(
              body: VideoPosterCarouselWidget(
                carousel: mockCarousel,
                source: 'test',
                pageIndex: 0,
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Wait for camera icon to disappear
      await tester.pump(const Duration(seconds: 4));
      expect(find.byIcon(Icons.camera_alt), findsNothing);

      // Find and tap the user photo element
      final userPhotoElement = mockCarousel.elements.firstWhere((element) => element.isUserPhoto);
      final userPhotoFinder = find.byWidgetPredicate((widget) =>
          widget is VideoPosterElementWidget && widget.element == userPhotoElement);
      
      await tester.tap(userPhotoFinder);
      await tester.pumpAndSettle();

      // Verify camera icon is visible again
      expect(find.byIcon(Icons.camera_alt), findsOneWidget);
    });

    test('video poster carousel view model should initialize camera icon visibility', () {
      // Get mock carousel with user photo
      final mockCarousel = getMockVideoPosterCarousel();
      
      // Create view model
      final viewModel = VideoPosterCarouselViewModel(
        MockVideoPostersService(),
        MockPosterPhotoUpdateConfig(),
      );

      // Initialize with carousel
      viewModel._init(carousel: mockCarousel);

      // Verify camera icon is initially visible (false means visible)
      expect(viewModel.disappearCameraIcon.value, isFalse);
    });

    test('video poster carousel should have videoPosterDeeplink', () {
      // Get mock carousel
      final mockCarousel = getMockVideoPosterCarousel();
      
      // Verify videoPosterDeeplink is set
      expect(mockCarousel.videoPosterDeeplink, isNotEmpty);
      expect(mockCarousel.videoPosterDeeplink, contains('photo_update'));
    });
  });
}

// Mock classes for testing
class MockVideoPostersService {
  // Add mock methods as needed
}

class MockPosterPhotoUpdateConfig {
  int get premiumPosterCameraDisplayTime => 3000;
}
